
const roomModelApi = require("../../models/Api/roomModel");
const commonFunction = require("../../assets/common");

/**Post /room/ createRoom Controller */
exports.createRoomController = async (request, response) => {
    try {
        const { room_name, company_id } = request.body;
        const roomDetails = await roomModelApi.createRoomModel(room_name, company_id);
        if (roomDetails) {
            // Update the newly created room with ISO code if found, otherwise use 999
            let roomIsoCode = await roomModelApi.roomIsoCode(room_name);
            const isoCodeToUpdate = (roomIsoCode && roomIsoCode.code) ? roomIsoCode.code : "999";
            await roomModelApi.updateRoomIsoCode(roomDetails.shipment_room_id, isoCodeToUpdate);

            commonFunction.generateResponse(
                response,
                SUCCESS_CODE,
                1,
                ROOM_ADDED_SUCCESS,
                roomDetails
            );
        }
        else
            commonFunction.generateResponse(
                response,
                SUCCESS_CODE,
                0,
                ROOM_ADDED_FAILURE,
                {}
            );
    }
    catch (reason) {
        console.log("exports.createRoomController -> error: ", reason);

        commonFunction.generateResponse(
            response,
            SERVER_ERROR_CODE,
            0,
            reason,
            {}
        )
    }
};


exports.getRoomListingController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		const roomListing = await roomModelApi.getRoomListingModel(request.query,request.body);
		if (roomListing)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_RETRIEVED_SUCCESS,
				roomListing
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
        console.log("exports.getRoomListingController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};