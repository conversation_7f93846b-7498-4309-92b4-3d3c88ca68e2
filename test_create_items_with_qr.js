const axios = require('axios');

// Test data for QR code assignment functionality
const testData = {
    "id": "69036a2896575614a2bb6931",
    "rooms": [
        {
            "name": "Living Room",
            "items": [
                {
                    "name": "Bar Stool",
                    "cft": 6,
                    "lbs": 42,
                    "carton": false,
                    "cp": true,
                    "note": "Test item 1"
                },
                {
                    "name": "Breakfast Table",
                    "cft": 10,
                    "lbs": 70,
                    "carton": false,
                    "cp": true,
                    "note": "Test item 2"
                }
            ]
        },
        {
            "name": "Bedroom",
            "items": [
                {
                    "name": "Bed Frame",
                    "cft": 15,
                    "lbs": 120,
                    "carton": false,
                    "cp": false,
                    "note": "Test item 3"
                }
            ]
        }
    ],
    "company_key": "YOUR_COMPANY_KEY_HERE"
};

async function testCreateItemsWithQR() {
    try {
        console.log('🚀 Testing Create Items API with QR Code Assignment...');
        console.log('📦 Creating 3 items across 2 rooms');
        
        const response = await axios.post('http://localhost:3000/api/create-items', testData, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ Success:', response.data);
        console.log('📊 Items created:', response.data.data.created_items);
        console.log('🏠 Rooms processed:', response.data.data.processed_rooms);
        
    } catch (error) {
        console.error('❌ Error:', error.response ? error.response.data : error.message);
    }
}

// QR Code Assignment Features Summary
console.log('🔧 QR Code Assignment Features:');
console.log('1. ✅ Checks for existing unassigned QR codes for the shipment');
console.log('2. ✅ Uses existing QR code if available');
console.log('3. ✅ Creates new QR code if no unassigned QR codes exist');
console.log('4. ✅ Assigns QR code to each created item');
console.log('5. ✅ Generates QR code image and uploads to S3');
console.log('6. ✅ Maintains proper QR code sequencing with label numbers');
console.log('7. ✅ Applies ISO code mapping for both rooms and items');
console.log('');
console.log('📝 To test:');
console.log('1. Update the company_key in testData');
console.log('2. Ensure shipment exists with movegistics ID: 69036a2896575614a2bb6931');
console.log('3. Uncomment the function call below');
console.log('');

// Uncomment to run the test
// testCreateItemsWithQR();

console.log('Test script ready. Update company_key and uncomment the function call to test.');
