# Create Items API with QR Code Assignment Documentation

## Endpoint
`POST /api/create-items`

## Description
This endpoint processes third-party data from Movegistics and creates items in the MOVER database. It maps Movegistics shipment data to the internal shipment structure, creating rooms and inventory items as needed. **NEW**: Includes automatic QR code assignment functionality for each created item.

## QR Code Assignment Features ✨

### 🔍 Smart QR Code Management
1. **Existing QR Code Check**: For each item, the system first checks if there are any unassigned QR codes for the shipment
2. **Reuse Logic**: If unassigned QR codes exist, the system assigns the oldest available QR code (by label number)
3. **Auto-Creation**: If no unassigned QR codes exist, the system creates a new QR code automatically
4. **Sequential Numbering**: New QR codes maintain proper sequence using the last label number + 1

### 🏷️ QR Code Generation Process
- **Random String**: 10-character alphanumeric string (A-Z, 0-9)
- **Image Generation**: QR code image created using QRCode library
- **S3 Upload**: QR code image uploaded to AWS S3 bucket
- **Database Storage**: QR code metadata stored with proper associations

### 📊 QR Code Properties
- **Type**: "Shipment" (linked to specific shipment)
- **Status**: "active"
- **Label Number**: Sequential numbering for organization
- **Image Path**: S3 path for QR code image
- **Job Association**: Linked to shipment via `job_id`

## Request Format

### Headers
```
Content-Type: application/json
```

### Body Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | string | Yes | Movegistics shipment ID |
| rooms | array | Yes | Array of room objects |
| company_key | string | Yes | Company API key for authentication |

### Room Object Structure
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| name | string | Yes | Room name |
| items | array | Yes | Array of item objects |

### Item Object Structure
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| name | string | Yes | Item name |
| cft | number | No | Volume in cubic feet |
| lbs | number | No | Weight in pounds |
| note | string | No | Additional notes |

## Response Format

### Success Response (200)
```json
{
  "status": 1,
  "message": "Items created successfully",
  "data": {
    "processed_rooms": 2,
    "created_items": 5,
    "qr_codes_assigned": 5,
    "qr_codes_created": 3,
    "qr_codes_reused": 2
  }
}
```

### Error Responses

#### Validation Error (400)
```json
{
  "status": 0,
  "message": "Validation failed",
  "errors": [
    {
      "field": "rooms",
      "message": "Rooms array is required"
    }
  ]
}
```

#### Shipment Not Found (404)
```json
{
  "status": 0,
  "message": "Shipment not found with the provided Movegistics ID"
}
```

## Processing Flow

### 1. Authentication & Validation
- Validates company API key
- Validates request structure
- Checks required fields

### 2. Shipment Lookup
- Finds existing shipment using Movegistics ID
- Returns error if shipment not found

### 3. Room Processing
For each room:
- Checks if room exists by name and company
- Creates new room if not found
- Applies ISO code mapping (from `room_iso_code` table or defaults to "999")

### 4. Item Creation with QR Assignment ⭐
For each item in each room:
- **QR Code Check**: Searches for unassigned QR codes for the shipment
- **QR Assignment Logic**:
  - If unassigned QR code found → Assign existing QR code
  - If no unassigned QR code → Create new QR code with image upload
- **Item Creation**: Creates inventory item with QR code assignment
- **ISO Code Mapping**: Applies item ISO code (from `item_iso_code` table or defaults to "999")

### 5. Response Generation
- Returns summary of processed rooms and created items
- Includes QR code assignment statistics

## Database Impact

### Tables Modified
- `shipment_room`: Room creation/lookup
- `shipment_inventory`: Item creation with QR assignment
- `qr_code`: QR code creation and management

### QR Code Table Fields
- `qr_code_id`: Primary key
- `job_id`: Links to shipment
- `random_number`: QR code string
- `qr_image`: S3 path to QR image
- `label_number`: Sequential number
- `type`: "Shipment"
- `status`: "active"

## Example Request
```json
{
  "id": "69036a2896575614a2bb6931",
  "rooms": [
    {
      "name": "Living Room",
      "items": [
        {
          "name": "Bar Stool",
          "cft": 6,
          "lbs": 42,
          "note": "Wooden bar stool"
        }
      ]
    }
  ],
  "company_key": "your-company-api-key"
}
```

## QR Code Benefits
- **Inventory Tracking**: Each item gets unique QR code for tracking
- **Efficient Management**: Reuses existing QR codes when possible
- **Automatic Generation**: Creates QR codes as needed
- **S3 Integration**: QR images stored in cloud for accessibility
- **Sequential Organization**: Maintains proper numbering system
