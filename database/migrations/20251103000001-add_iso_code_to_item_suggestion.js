'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'item_suggestion',
        'iso_code',
        {
          type: Sequelize.STRING,
          allowNull: true
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('item_suggestion', 'iso_code');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
