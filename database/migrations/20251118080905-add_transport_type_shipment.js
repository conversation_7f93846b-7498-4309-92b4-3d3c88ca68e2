'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_jobs',
        'transport_type_id',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "transport_type_codes",
            key: "transport_type_id",
          },
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_jobs', 'transport_type_id');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
