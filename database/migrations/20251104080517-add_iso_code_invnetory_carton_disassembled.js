'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventories',
        'iso_code_carton',
        {
          type: Sequelize.STRING,
          allowNull: true
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_inventories',
        'iso_code_disassembled',
        {
          type: Sequelize.STRING,
          allowNull: true
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_inventories', 'iso_code_carton');
      await queryInterface.removeColumn('shipment_inventories', 'iso_code_disassembled');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
