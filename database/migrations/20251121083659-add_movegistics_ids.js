'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_jobs',
        'movegistics_shipment_job_id',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'customers',
        'movegistics_customer_id',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_jobs', 'movegistics_shipment_job_id');
      await queryInterface.removeColumn('customers', 'movegistics_customer_id');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
