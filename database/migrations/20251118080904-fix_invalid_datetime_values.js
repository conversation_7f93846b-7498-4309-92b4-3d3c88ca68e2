'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Temporarily disable strict mode to handle invalid dates
      await queryInterface.sequelize.query(`SET sql_mode = 'ALLOW_INVALID_DATES'`);

      // Fix invalid datetime values in shipment_jobs table using CASE statement
      await queryInterface.sequelize.query(`
        UPDATE shipment_jobs
        SET pickup_date = CASE
          WHEN pickup_date = '0000-00-00 00:00:00' OR pickup_date = '0000-00-00' THEN NULL
          ELSE pickup_date
        END
      `);

      await queryInterface.sequelize.query(`
        UPDATE shipment_jobs
        SET delivery_date = CASE
          WHEN delivery_date = '0000-00-00 00:00:00' OR delivery_date = '0000-00-00' THEN NULL
          ELSE delivery_date
        END
      `);

      // Fix any other datetime columns that might have invalid values
      await queryInterface.sequelize.query(`
        UPDATE shipment_jobs
        SET created_at = CASE
          WHEN created_at = '0000-00-00 00:00:00' OR created_at = '0000-00-00' THEN NOW()
          <PERSON>LS<PERSON> created_at
        END
      `);

      await queryInterface.sequelize.query(`
        UPDATE shipment_jobs
        SET updated_at = CASE
          WHEN updated_at = '0000-00-00 00:00:00' OR updated_at = '0000-00-00' THEN NOW()
          ELSE updated_at
        END
      `);

      // Restore strict mode
      await queryInterface.sequelize.query(`SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'`);

      console.log('✅ Fixed invalid datetime values in shipment_jobs table');
      return Promise.resolve();
    } catch (e) {
      console.error('❌ Error fixing datetime values:', e);
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // This migration cannot be easily reversed as we're fixing data integrity issues
    // The down migration is intentionally left empty
    console.log('⚠️  Cannot reverse datetime fix migration - this is expected');
    return Promise.resolve();
  },
};
