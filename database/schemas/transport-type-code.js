"use strict";
module.exports = (sequelize, DataTypes) => {
	const transport_type_code = sequelize.define(
		"transport_type_codes",
		{
			transport_type_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			code: DataTypes.STRING,
			created_at: {
				type: DataTypes.DATE,
			},
			updated_at: {
				type: DataTypes.DATE,
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	transport_type_code.associate = function (models) {
	};
	return transport_type_code;
};
