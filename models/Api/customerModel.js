
const {
	shipment_job,
	company_token,
	company_integration_key,
	customer,
	tag_customer,
	tag,
	sequelize,
	company,
} = require("../../database/schemas");
const { Op, literal } = require("sequelize");

exports.getCustomerMovegisticsIdCheck = async (request) => {
	return await customer.findOne({
		where: {
			movegistics_customer_id: request.movegistics_customer_id,
		},
	});
};

exports.checkReExistingEmailAdd = async (request) => {
	return await customer.findOne({
		where: {
			email: request.email,
			[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
		},
	});
};

exports.getCustomerStorageID = async (request) => 
	await customer.findOne({
		where:{
			customer_id : request.customer_id
		}
	})

exports.getCompanyData = async (request) =>
	await company_integration_key.findOne({
		where: {
			company_id: request.company_id,
			status: "active"
		},
		attributes: [
			"company_integration_key_id",
			"company_id",
			"integration_key",
			"company_identity",
			"status",
		],
		include: {
			model: company,
			as: "key_company",
			attributes: ["company_name", "company_id", "storage_company_id"],
		},
	})

exports.getCustomerList = async (company_id, request) => {
	const status = request.filter ? request.filter : "active";
	const pageNo = request.pageNo ? request.pageNo : 1;
	const pageSize = request.pageSize ? request.pageSize : 10;
	const orderBy = request.orderBy ? request.orderBy : "created_at";
	const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
	const search = request.search ? request.search : "";
	pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);

	return await customer.findAndCountAll({
		limit: parseInt(pageSize),
		offset: parseInt(start),
		where: company_id
			? {
				[Op.and]: [
					{ company_id: company_id },
					{
						[Op.or]: [
							// { customer_name: { [Op.like]: "%" + search + "%" } },
							{ first_name: { [Op.like]: "%" + search + "%" } },
							{ last_name: { [Op.like]: "%" + search + "%" } },
							{ email: { [Op.like]: "%" + search + "%" } },
							{ account_id: { [Op.like]: "%" + search + "%" } },
						],
					},
				],
				is_deleted: "0",
				status: status,
			}
			: {
				[Op.or]: [
					// { customer_name: { [Op.like]: "%" + search + "%" } },
					{ first_name: { [Op.like]: "%" + search + "%" } },
					{ last_name: { [Op.like]: "%" + search + "%" } },
					{ email: { [Op.like]: "%" + search + "%" } },
					{ account_id: { [Op.like]: "%" + search + "%" } },
				],
				is_deleted: "0",
				status: status,
			},
		order: [[orderBy, orderSequence]],
		attributes: [
			...COMMON_CUSTOMER_ATTRIBUTES,
			[
				sequelize.literal(
					"(SELECT company_name from `companies` where company_id = `customer`.company_id)"
				),
				"company_name",
			],
			[
				sequelize.literal(
					`(CASE WHEN customer.photo IS NULL THEN '' ELSE customer.photo END)`
				),
				"photo",
			],
			[
				sequelize.literal(
					`(CASE WHEN customer.photo IS NULL THEN '' WHEN customer.photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Customer_Profile}', 'original/', customer.photo) ELSE customer.photo END)`
				),
				"customer_profile",
			],
			// [sequelize.literal(`(CASE WHEN photo IS NULL THEN '' ELSE photo END)`), "photo"],
		],
		distinct: true,
		include: [
			{
				model: tag_customer,
				as: "customer_tag",
				required: false,
				attributes: ["tag_id"],
				include: {
					model: tag,
					as: "m2m_customer_tag",
					attributes: COMMON_TAG_ATTRIBUTES,
				},
			},
		],
	});
};