const {
	shipment_job,
	company,
	shipment_type_stage,
	shipment_type,
	shipment_job_assign_worker,
	shipment_job_assign_worker_list,
	customer,
	shipment_job_forced,
	shipment_job_signature,
	shipment_type_for_shipment,
	shipment_type_stage_for_shipment,
	qr_code,
	staff,
	shipment_inventory,
	shipment_inventory_photo,
	shipment_room,
	shipment_inventory_exception,
	shipment_inventory_comments,
	shipment_inventory_notes,
	shipment_exception,
	shipment_inventory_exception_note,
	shipment_inventory_location,
	shipment_location,
	tag_shipment,
	tag,
	unit_list,
	sequelize,
} = require("../../database/schemas");
const { request } = require("../../routes/openApiRoutes");
const { where, fn, col, Op } = require("sequelize");

exports.createItem = async (itemData) => {
	return await shipment_inventory.create(itemData);
}

exports.shipmentMovegisticsIdCheck = async (movegistics_shipment_job_id) => {
	return await shipment_job.findOne({
		where: {
			movegistics_shipment_job_id: movegistics_shipment_job_id,
		},
	});
};

exports.fetchShipmentData = async (body) =>
	await shipment_job.findOne({
		where: {
			shipment_job_id: body.shipment_job_id,
		}
	})


exports.shipmentStorageIdUpdate = async (shipmentIdStorage, shipmentId) =>
	await shipment_job.update(
		{
			storage_shipment_job_id: shipmentIdStorage,
		},
		{ where: { shipment_job_id: shipmentId } }
	);



exports.companyKeyValidationForJobId = async (request, companyId) => {
	return shipment_job.findOne({
		where: {
			shipment_job_id: request,
			company_id: companyId.company_id
		}
	})
}

exports.companyKeyValidationForShipmentId = async (request, companyId) => {
	return shipment_job.findOne({
		where: {
			shipment_job_id: request,
			company_id: companyId.company_id
		}
	})
}


exports.listShipmentModel = async (company_id, fieldsAndValues) =>
	await shipment_job.findAndCountAll({
		limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
		offset:
			fieldsAndValues.page_no > 1
				? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
				: 0,
		where: company_id
			? {
				[Op.and]: [
					{ company_id: company_id },
					{
						[Op.or]: [
							{
								shipment_job_id: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								job_number: { [Op.like]: "%" + fieldsAndValues.search + "%" },
							},
							{
								email: { [Op.like]: "%" + fieldsAndValues.search + "%" },
							},
							where(
								fn("concat", col("`customer_job.first_name`"), " ", col("`customer_job.last_name`")),
								{
									[Op.like]: "%" + fieldsAndValues.search + "%",
								}
							),
							{
								shipment_name: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								pickup_date: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								pickup_address: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								pickup_state: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								pickup_city: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								pickup_zipcode: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								pickup_country: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								delivery_address: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								delivery_state: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								delivery_city: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								delivery_zipcode: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								delivery_country: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								contact_reference: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								account_reference: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								opportunity_reference: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								wo_reference: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								external_reference: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								external_reference_2: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								external_reference_3: {
									[Op.like]: "%" + fieldsAndValues.search + "%",
								},
							},
							{
								created_at: { [Op.like]: "%" + fieldsAndValues.search + "%" },
							},
						],
					},
				],
			}
			: {
				[Op.or]: [
					{
						shipment_job_id: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						email: { [Op.like]: "%" + fieldsAndValues.search + "%" },
					},
					where(fn("concat", col("`customer_job.first_name`"), " ", col("`customer_job.last_name`")), {
						[Op.like]: "%" + fieldsAndValues.search + "%",
					}),
					{
						shipment_name: { [Op.like]: "%" + fieldsAndValues.search + "%" },
					},
					{ job_number: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
					{ pickup_date: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
					{
						pickup_address: { [Op.like]: "%" + fieldsAndValues.search + "%" },
					},
					{
						pickup_state: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						pickup_city: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						pickup_zipcode: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						pickup_country: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						delivery_address: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						delivery_state: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						delivery_city: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						delivery_zipcode: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						delivery_country: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						contact_reference: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						account_reference: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						opportunity_reference: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						wo_reference: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						external_reference: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						external_reference_2: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{
						external_reference_3: {
							[Op.like]: "%" + fieldsAndValues.search + "%",
						},
					},
					{ created_at: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
				],
			},
		attributes: COMMON_JOB_SHIPMENT_ATTRIBUTES,
		order: [
			[
				fieldsAndValues.order_by_fields ? fieldsAndValues.order_by_fields : "created_at",
				fieldsAndValues.order_sequence ? fieldsAndValues.order_sequence : "DESC",
			],
		],
		distinct: true,
		include: [
			{
				model: tag_shipment,
				as: "shipment_tag",
				required: false,
				attributes: [COMMON_JOB_TAG_ATTRIBUTES[0]],
				include: {
					model: tag,
					as: "m2m_tag",
					attributes: COMMON_TAG_ATTRIBUTES,
				},
			},
			{
				model: shipment_type_for_shipment,
				as: "shipment_type_for_shipment",
				attributes: ["local_shipment_type_id", "name"],
			},
			{
				model: shipment_type_stage_for_shipment,
				as: "local_shipment_job_status",
				attributes: [
					...BASIC_JOB_STAGES_ATTRIBUTES2,
					[
						sequelize.literal(
							`CASE WHEN (select local_altered_stage_id from shipment_job_forced where shipment_job_id = shipment_job.shipment_job_id and local_job_status = local_altered_stage_id order by forced_status_id desc limit 1) is not null THEN true ELSE false end`
						),
						"is_forced",
					],
				],
			},
			{
				model: customer,
				as: "customer_job",
				required: true,
				attributes: [
					"customer_id",
					"email",
					[
						sequelize.literal(
							'CASE WHEN last_name is NULL then first_name ELSE CONCAT(first_name," ",last_name) end'
						),
						"full_name",
					],
					"is_invited",
				],
			},
		],
	});



exports.isValidShipmentIdModel = async (shipmentId) =>
	await shipment_job.findByPk(shipmentId, {
		attributes: ["shipment_job_id", "customer_id", "email"],
	});


exports.getShipmentDetailModel = async (company_id, shipmentId, orderingMethod, orderingWay) => {
	const dbValue = await shipment_job
		.findOne({
			where: company_id
				? {
					shipment_job_id: shipmentId,
					company_id: company_id,
				}
				: {
					shipment_job_id: shipmentId,
				},
			include: [
				{
					model: shipment_type_for_shipment,
					as: "shipment_type_for_shipment",
					attributes: [
						"local_shipment_type_id",
						[
							sequelize.literal(
								"(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
							),
							"current_job_stage",
						],
						[
							sequelize.literal(
								"(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
							),
							"total_stages",
						],
					],
					include: [
						{
							model: shipment_type_stage_for_shipment,
							as: "local_shipment_stage",
							attributes: [
								...BASIC_JOB_STAGES_ATTRIBUTES2,
								"order_of_stages",
								[
									sequelize.literal(
										`(select shipment_job_signatures.created_at from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
									),
									"created_at",
								],
								[
									sequelize.literal(
										`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
										}',customer_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
									),
									"customer_signature",
								],
								[
									sequelize.literal(
										`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
										}',supervisor_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id order by shipment_job_signature_id desc limit 1)`
									),
									"supervisor_signature",
								],

								[
									sequelize.literal(
										`(CASE WHEN supervisor_signature_require_at_origin_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"supervisor_signature_require_at_origin_to_all_pages",
								],
								[
									sequelize.literal(
										`(CASE WHEN supervisor_signature_require_at_destination_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"supervisor_signature_require_at_destination_to_all_pages",
								],

								[
									sequelize.literal(
										`(CASE WHEN customer_signature_require_at_origin_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"customer_signature_require_at_origin_to_all_pages",
								],

								[
									sequelize.literal(
										`(CASE WHEN customer_signature_require_at_destination_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"customer_signature_require_at_destination_to_all_pages",
								],
							],
							// order: "order_of_stages",
							where: { status: "active" },
							include: [
								{
									model: shipment_job_forced,
									as: "shipment_job_forced",
									required: false,
									attributes: COMMON_JOB_FORCED_SHIPMENT_ATTRIBUTES,
									where: { shipment_job_id: shipmentId },
								},
							],
						},
					],
				},
				// {
				// 	model: shipment_job_assign_worker,
				// 	required: false,
				// 	attributes: [
				// 		"staff_id",
				// 		"role",
				// 		[
				// 			sequelize.literal("(select first_name FROM `staffs` where staff_id = job_worker.staff_id) "),
				// 			"first_name",
				// 		],
				// 		[
				// 			sequelize.literal("(select last_name FROM `staffs` where staff_id = job_worker.staff_id) "),
				// 			"last_name",
				// 		],
				// 	],
				// 	as: "job_worker",
				// 	include: [
				// 		{
				// 			model: staff,
				// 			attributes: [],
				// 			required: true,
				// 			as: "job_worker_detail",
				// 		},
				// 	],
				// },
				{
					model: tag_shipment,
					as: "shipment_tag",
					required: false,
					attributes: [COMMON_JOB_TAG_ATTRIBUTES[0]],
					include: {
						model: tag,
						as: "m2m_tag",
						attributes: COMMON_TAG_ATTRIBUTES,
					},
				},
				{
					model: customer,
					as: "customer_job",
					attributes: COMMON_CUSTOMER_ATTRIBUTES,
				},
				{
					model: shipment_inventory,
					attributes: [
						...COMMON_INVENTORY_ATTRIBUTES,
						[
							sequelize.literal(
								`(IF((select shipment_job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.current_stage_id = shipment_job.job_status and shipment_inventory_job_scanned.shipment_inventory_id = job_items.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = job_items.shipment_job_id) is not null, 'yes', 'no'))`
							),
							"isScanned",
						],
						[sequelize.literal(`(CASE WHEN is_carton = '1' THEN 'Yes' ELSE 'No' END)`), "is_carton"],
						[
							sequelize.literal(
								`(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.current_stage_id = shipment_job.job_status and shipment_inventory_forced.shipment_inventory_id = job_items.shipment_inventory_id) is not null, 'yes', 'no'))`
							),
							"isOverride",
						],
						[sequelize.literal(`(IF(packed_by_owner = '1', 'OWNER', 'MOVER'))`), "packed_by"],
						[
							sequelize.literal(
								`(CASE WHEN disassembled_by_owner = '1' THEN 'By Customer' ELSE 'By Company' END)`
							),
							"disassembled_by",
						],
						[sequelize.literal(`(IF(is_disassembled = '1', 'YES', 'NO'))`), "is_disassembled"],
						[sequelize.literal(`(IF(is_electronics = '1', 'YES', "NO"))`), "is_electronics"],
						[
							sequelize.literal(`(CASE WHEN is_high_value = '1' THEN 'true' ELSE 'false' END)`),
							"is_high_value",
						],
						[
							sequelize.literal(`(CASE WHEN is_pro_gear = '1' THEN 'true' ELSE 'false' END)`),
							"is_pro_gear",
						],
						[
							sequelize.literal(`(CASE WHEN is_firearm = '1' THEN 'true' ELSE 'false' END)`),
							"is_firearm",
						],
						[
							sequelize.literal(`(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`),
							"isManualLabel",
						],
						[
							sequelize.literal(`(IF(is_electronics = '1', 'YES', "NO"))`), "is_electronics"
						],
					],
					required: false,
					as: "job_items",
					include: [
						{
							model: shipment_room,
							as: "room",
							attributes: ["name"],
						},
						{
							model: shipment_inventory_comments,
							attributes: ["id",
								"shipment_inventory_id",
								"comment",],
							as: "comments",
						},
						{
							model: shipment_inventory_notes,
							attributes: ["id",
								"shipment_inventory_id",
								"note",],
							as: "inventory_notes",
						},
						{
							model: shipment_inventory_exception_note,
							attributes: [["shipment_inventory_exception_note_id", "note_id"], "notes"],
							required: false,
							as: "exceptions",
							include: [
								{
									model: shipment_inventory_exception,
									required: false,
									attributes: [
										"shipment_exception_id",
										[
											sequelize.literal(
												"(select name FROM `shipment_exceptions` where shipment_exception_id = `job_items->exceptions->eid`.`shipment_exception_id`) "
											),
											"exception_name",
										],
									],
									as: "eid",
									include: [
										{
											model: shipment_exception,
											attributes: [],
											required: true,
											as: "exception_list",
										},
									],
								},
								{
									model: shipment_inventory_location,
									required: false,
									attributes: [
										"shipment_location_id",
										[
											sequelize.literal(
												"(select name FROM `shipment_locations` where shipment_location_id = `job_items->exceptions->lid`.`shipment_location_id`) "
											),
											"location_name",
										],
									],
									as: "lid",
									include: [
										{
											model: shipment_location,
											attributes: [],
											required: true,
											as: "location_list",
										},
									],
								},
							],
						},
						{
							model: qr_code,
							as: "item_qr",
							attributes: [
								"random_number",
								"type",
								[
									sequelize.literal(
										`LPAD(label_number, 8, 0)`
									),
									"label_number",
								],
							],
						},
						{
							model: staff,
							as: "disassembled_user",
							attributes: ["first_name", "last_name"],
						},
						{
							model: shipment_inventory_photo,
							attributes: [
								["shipment_inventory_photo_id", "photo_id"],
								"description",
								"stage_id",
								"local_stage_id",
								"media",
								[
									sequelize.literal(
										`(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', job_items.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', job_items.shipment_inventory_id, '/original/', media) END)`
									),
									"item_photo",
								],
							],
							required: false,
							as: "item_photos",
						},
					],
				},
				{
					model: company,
					as: "job_company",
					required: false,
					attributes: [
						...COMMON_COMPANY_ATTRIBUTES,
						[
							sequelize.literal(
								`(CASE WHEN job_company.photo IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE_Company_Profile}', 'original/', job_company.photo) END)`
							),
							"company_logo",
						],
					],
				},
			],
			attributes: [
				...COMMON_JOB_ATTRIBUTES,
				"shipment_name",
				[
					sequelize.literal(`(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`),
					"is_job_complete",
				],
				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_items",
				],

				[
					sequelize.literal(
						'(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_volume",
				],
				[
					sequelize.literal(
						'(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
					),
					"firearms_total_quantity",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
					),
					"total_cartons",
				],


				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
					),
					"total_cartons_cp",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
					),
					"total_cartons_pbo",
				],

				[
					sequelize.literal(
						'(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
					),
					"total_high_value",
				],

				[
					sequelize.literal(
						'(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_pads_used",
				],

				[
					sequelize.literal(
						'(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_disassembled = "1")'
					),
					"total_disassembled_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_electronics = "1")'
					),
					"total_electronics_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1")'
					),
					"total_highValue_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1")'
					),
					"total_is_pro_gear_items",
				],

				[
					sequelize.literal(
						"(IF((select shipment_stage_id from shipment_type_stages inner join shipment_types st on shipment_type_stages.shipment_type_id = st.shipment_type_id where st.shipment_type_id = shipment_job.shipment_type_id order by order_of_stages desc limit 1) = job_status,  `shipment_job`.updated_at, ''))"
					),
					"actual_delivery",
				],
			],
			order: [
				[
					"job_items",
					orderingMethod ? orderingMethod : "created_at",
					orderingWay ? orderingWay : "DESC",
				],
				["shipment_type_for_shipment", "local_shipment_stage", "order_of_stages", "ASC"],
			],
		});
	if (dbValue) return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)));
}

exports.getShipmentDetailModelForPdf = async (shipmentId) => {
	const dbValue = await shipment_job
		.findOne({
			where: {
				shipment_job_id: shipmentId,
			},
			include: [
				{
					model: shipment_type_for_shipment,
					as: "shipment_type_for_shipment",
					attributes: [
						"local_shipment_type_id",
						"ref_shipment_type_id",
						"name",
						"number_of_stages",
						"signature_require",
						"status",
						[
							sequelize.literal(
								"(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
							),
							"current_job_stage",
						],
						[
							sequelize.literal(
								"(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
							),
							"total_stages",
						],
					],
					include: [
						{
							model: shipment_type_stage_for_shipment,
							as: "local_shipment_stage",
							attributes: [
								...BASIC_JOB_STAGES_ATTRIBUTES2,
								"order_of_stages",
								[
									sequelize.literal(
										`(select shipment_job_signatures.created_at from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
									),
									"created_at",
								],
								[
									sequelize.literal(
										`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
										}',customer_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
									),
									"customer_signature",
								],
								[
									sequelize.literal(
										`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
										}',supervisor_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id order by shipment_job_signature_id desc limit 1)`
									),
									"supervisor_signature",
								],

								[
									sequelize.literal(
										`(CASE WHEN supervisor_signature_require_at_origin_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"supervisor_signature_require_at_origin_to_all_pages",
								],
								[
									sequelize.literal(
										`(CASE WHEN supervisor_signature_require_at_destination_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"supervisor_signature_require_at_destination_to_all_pages",
								],

								[
									sequelize.literal(
										`(CASE WHEN customer_signature_require_at_origin_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"customer_signature_require_at_origin_to_all_pages",
								],

								[
									sequelize.literal(
										`(CASE WHEN customer_signature_require_at_destination_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"customer_signature_require_at_destination_to_all_pages",
								],
							],
							order: "order_of_stages",
							where: { status: "active" },
							include: [
								{
									model: shipment_job_forced,
									as: "shipment_job_forced",
									required: false,
									attributes: COMMON_JOB_FORCED_SHIPMENT_ATTRIBUTES2,
									where: { shipment_job_id: shipmentId },
								},
							],
						},
					],
				},
				{
					model: shipment_job_assign_worker_list,
					required: false,
					attributes: [
						"staff_id",
						"role",
						[
							sequelize.literal("(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
							"first_name",
						],
						[
							sequelize.literal("(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
							"last_name",
						],
					],
					as: "assign_worker",
					include: [
						{
							model: staff,
							attributes: [],
							required: true,
							as: "assign_worker_detail",
						},
					],
				},
				{
					model: tag_shipment,
					as: "shipment_tag",
					required: false,
					attributes: [COMMON_JOB_TAG_ATTRIBUTES[0]],
					include: {
						model: tag,
						as: "m2m_tag",
						attributes: COMMON_TAG_ATTRIBUTES,
					},
				},
				{
					model: customer,
					as: "customer_job",
					attributes: COMMON_CUSTOMER_ATTRIBUTES,
				},
				{
					model: shipment_inventory,
					attributes: [
						...COMMON_INVENTORY_ATTRIBUTES,
						[
							sequelize.literal(
								`(IF((select shipment_job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.current_stage_id = shipment_job.job_status and shipment_inventory_job_scanned.shipment_inventory_id = job_items.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = job_items.shipment_job_id) is not null, 'yes', 'no'))`
							),
							"isScanned",
						],
						[sequelize.literal(`(CASE WHEN is_carton = '1' THEN 'Yes' ELSE 'No' END)`), "is_carton"],
						[
							sequelize.literal(
								`(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.current_stage_id = shipment_job.job_status and shipment_inventory_forced.shipment_inventory_id = job_items.shipment_inventory_id) is not null, 'yes', 'no'))`
							),
							"isOverride",
						],
						[sequelize.literal(`(IF(packed_by_owner = '1', 'OWNER', 'MOVER'))`), "packed_by"],
						[
							sequelize.literal(
								`(CASE WHEN disassembled_by_owner = '1' THEN 'By Customer' ELSE 'By Company' END)`
							),
							"disassembled_by",
						],
						[sequelize.literal(`(IF(is_disassembled = '1', 'YES', 'NO'))`), "is_disassembled"],
						[sequelize.literal(`(IF(is_electronics = '1', 'YES', "NO"))`), "is_electronics"],
						[
							sequelize.literal(`(CASE WHEN is_high_value = '1' THEN 'true' ELSE 'false' END)`),
							"is_high_value",
						],
						[
							sequelize.literal(`(CASE WHEN is_pro_gear = '1' THEN 'true' ELSE 'false' END)`),
							"is_pro_gear",
						],
						[
							sequelize.literal(`(CASE WHEN is_firearm = '1' THEN 'true' ELSE 'false' END)`),
							"is_firearm",
						],
						[
							sequelize.literal(`(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`),
							"isManualLabel",
						],
						[
							sequelize.literal(`(IF(is_electronics = '1', 'YES', "NO"))`), "is_electronics"
						],
					],
					required: false,
					as: "job_items",
					include: [
						{
							model: shipment_room,
							as: "room",
							attributes: ["name"],
						},
						{
							model: shipment_inventory_comments,
							attributes: ["id",
								"shipment_inventory_id",
								"comment",],
							as: "comments",
						},
						{
							model: shipment_inventory_notes,
							attributes: ["id",
								"shipment_inventory_id",
								"note",],
							as: "inventory_notes",
						},
						{
							model: shipment_inventory_exception_note,
							attributes: [["shipment_inventory_exception_note_id", "note_id"], "notes"],
							required: false,
							as: "exceptions",
							include: [
								{
									model: shipment_inventory_exception,
									required: false,
									attributes: [
										"shipment_exception_id",
										[
											sequelize.literal(
												"(select name FROM `shipment_exceptions` where shipment_exception_id = `job_items->exceptions->eid`.`shipment_exception_id`) "
											),
											"exception_name",
										],
									],
									as: "eid",
									include: [
										{
											model: shipment_exception,
											attributes: [],
											required: true,
											as: "exception_list",
										},
									],
								},
								{
									model: shipment_inventory_location,
									required: false,
									attributes: [
										"shipment_location_id",
										[
											sequelize.literal(
												"(select name FROM `shipment_locations` where shipment_location_id = `job_items->exceptions->lid`.`shipment_location_id`) "
											),
											"location_name",
										],
									],
									as: "lid",
									include: [
										{
											model: shipment_location,
											attributes: [],
											required: true,
											as: "location_list",
										},
									],
								},
							],
						},
						{
							model: qr_code,
							as: "item_qr",
							attributes: [
								"random_number",
								"type",
								[
									sequelize.literal(
										`LPAD(label_number, 8, 0)`
									),
									"label_number",
								],
							],
						},
						{
							model: staff,
							as: "disassembled_user",
							attributes: ["first_name", "last_name"],
						},
						{
							model: shipment_inventory_photo,
							attributes: [
								["shipment_inventory_photo_id", "photo_id"],
								"media",
								[
									sequelize.literal(
										`(CASE WHEN media IS NOT NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', job_items.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', job_items.shipment_inventory_id, '/original/', media) END)`
									),
									"item_photo",
								],
							],
							required: false,
							as: "item_photos",
						},
						{
							model: shipment_inventory_comments,
							attributes: ["id", "shipment_inventory_id", "comment"],
							as: "comments",
						},
						{
							model: shipment_inventory_notes,
							attributes: ["id", "shipment_inventory_id", "note"],
							as: "inventory_notes",
						},
					],
				},
				{
					model: company,
					as: "job_company",
					required: false,
					attributes: [
						...COMMON_COMPANY_ATTRIBUTES,
						[
							sequelize.literal(
								`(CASE WHEN job_company.photo IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE_Company_Profile}', 'original/', job_company.photo) END)`
							),
							"company_logo",
						],
					],
				},
			],
			attributes: [
				...COMMON_JOB_ATTRIBUTES,
				"shipment_name",
				[
					sequelize.literal(`(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`),
					"is_job_complete",
				],
				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_items",
				],

				[
					sequelize.literal(
						'(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_volume",
				],
				[
					sequelize.literal(
						'(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
					),
					"firearms_total_quantity",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
					),
					"total_cartons",
				],


				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
					),
					"total_cartons_cp",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
					),
					"total_cartons_pbo",
				],

				[
					sequelize.literal(
						'(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
					),
					"total_high_value",
				],

				[
					sequelize.literal(
						'(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_pads_used",
				],

				[
					sequelize.literal(
						'(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_disassembled = "1")'
					),
					"total_disassembled_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_electronics = "1")'
					),
					"total_electronics_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1")'
					),
					"total_highValue_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1")'
					),
					"total_is_pro_gear_items",
				],

				[
					sequelize.literal(
						"(IF((select shipment_stage_id from shipment_type_stages inner join shipment_types st on shipment_type_stages.shipment_type_id = st.shipment_type_id where st.shipment_type_id = shipment_job.shipment_type_id order by order_of_stages desc limit 1) = job_status,  `shipment_job`.updated_at, ''))"
					),
					"actual_delivery",
				],
			],
		});
	if (dbValue) return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)));
}

exports.validWarehouseIdController = async (warehouseId) =>
	await unit_list.findOne({
		where: {
			warehouseId: warehouseId,
		}
	})

exports.unitTypeList = async (warehouseId) =>
	await unit_list.findAndCountAll({
		where: {
			warehouseId: warehouseId,
		}
	})

