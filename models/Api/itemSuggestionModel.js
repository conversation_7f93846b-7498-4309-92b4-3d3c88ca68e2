const { item_suggestion: item, item_iso_code } = require("../../database/schemas");
const { Op, literal } = require("sequelize");



exports.createItemSuggestionModel = async (itemData) => {
    const itemDetails = await item.create({
        name: itemData.item_name,
        volume: itemData.item_volume,
        weight: itemData.item_weight,
        admin_id: null,
        company_id: itemData.company_id,
        staff_id: null
    });

    // Update the newly created item with ISO code if found, otherwise use 999
    if (itemDetails) {
        let itemIsoCode = await exports.itemIsoCode(itemData.item_name);
        const isoCodeToUpdate = (itemIsoCode && itemIsoCode.code) ? itemIsoCode.code : "999";
        await exports.updateItemIsoCode(itemDetails.item_suggestion_id, isoCodeToUpdate);
    }

    return itemDetails;
}

exports.getItemListingModel = async (fieldsAndValues, body) => {
    return await item.findAndCountAll({
        limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
        offset:
            fieldsAndValues.page_no > 1
                ? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
                : 0,
        where: {
            company_id: body.company_id,
            [Op.or]: [{ name: { [Op.like]: "%" + fieldsAndValues.search + "%" } }],
            status: fieldsAndValues.filter ? fieldsAndValues.filter : "active",
        },
        attributes: ["item_suggestion_id", "name", "volume", "weight", "status", "admin_id", "company_id", "staff_id"],
        order: [
            [
                fieldsAndValues.order_by_fields
                    ? fieldsAndValues.order_by_fields
                    : "created_at",
                fieldsAndValues.order_sequence
                    ? fieldsAndValues.order_sequence
                    : "DESC",
            ],
        ],
    });
}

exports.itemIsoCode = async (itemName) => {
    return item_iso_code.findOne({
        where: {
            name: itemName,
        }
    });
};

exports.updateItemIsoCode = async (itemId, isoCode) => {
    return await item.update(
        { iso_code: isoCode },
        { where: { item_suggestion_id: itemId } }
    );
};

