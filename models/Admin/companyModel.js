const { company, company_token, sequelize, customer, shipment_job, shipment_job_assign_worker, shipment_job_assign_worker_list, unit_list, staff, company_integration_key } = require("../../database/schemas");
const { Op, literal } = require("sequelize");
const shipmentTypeModel = require("../../models/Admin/shipmentTypeModel");
const customerModel = require("../../models/Admin/customerModel");
const StaffModel = require("../../models/Admin/staffModel");
const roomModel = require("../../models/Admin/roomModel");
const itemSuggestionModel = require("../../models/Admin/itemSuggestionModel");
const tagModel = require("../../models/Admin/tagModel");
const bcrypt = require("bcrypt");
const { includes } = require("lodash");

exports.fetchCompanyKeyDetails = async (companyId) => {
	return company_token.findOne({
		where: {
			company_id: companyId
		},
	})
}

exports.fetchCompanyNameForGenericLable = async (companyId) => {
	return await company.findOne({
		where: {
			company_id: companyId
		},
		attributes: ["company_name"]
	})
}

exports.companyAllUserSoftDelete = async (request) => {
	return await staff.update(
		{
			is_deleted: literal('CASE WHEN is_deleted = "0" THEN "1" ELSE "1" END'),
		},
		{ where: { company_id: request.company_id } }
	);
}

exports.companyAllCustomerSoftDelete = async (request) => {
	return await customer.update(
		{
			is_deleted: literal('CASE WHEN is_deleted = "0" THEN "1" ELSE "1" END'),
		},
		{ where: { company_id: request.company_id } }
	);
}

exports.fetchCompanyForStorage = async (body) => {
	return company.findOne({
		where: {
			company_id: body.company_id
		},
		attributes: [
			"company_id",
			"storage_company_id",
			"status",
			"email",
			[
				sequelize.literal(
					"(select integration_key FROM `company_integration_keys` where company_id = company.company_id) "
				),
				"integration_key",
			],
		]
	})
}

exports.fetchCompanyIdForStaff = async (companyId) => {
	return company.findOne({
		where: {
			company_id: companyId
		},
		attributes: [
			"company_id",
			"storage_company_id",
			"company_identity",
			"status",
			"email",
			[
				sequelize.literal(
					"(select integration_key FROM `company_integration_keys` where company_id = company.company_id) "
				),
				"integration_key",
			],
		]
	})
}

exports.getCompanyIdForStaff = async (body) => {
	return company.findOne({
		where: {
			storage_company_id: body.storage_company_id
		}
	})
}

exports.fetchCompanyByStorage = async (body) => {
	return company.findOne({
		where: {
			storage_company_id: body.company_id
		}
	})
}

exports.superAdminCompanyUpdatePasswordFindCompany = async (companyId) => {
	return company.findOne({
		where: {
			company_id: companyId
		}
	})
}

exports.superAdminCompanyUpdatePassword = async (fields) => {
	const salt = bcrypt.genSaltSync(10);
	let pwd = bcrypt.hashSync(fields.new_password, salt);
	let updatePwd = await company.update(
		{ password: pwd, verification_code: null },
		{ where: { company_id: fields.company_id } }
	);
	return updatePwd;
};

exports.superAdminStaffUpdatePassword = async (staffId, new_password) => {
	const salt = bcrypt.genSaltSync(10);
	let pwd = bcrypt.hashSync(new_password, salt);
	let updatePwd = await staff.update(
		{ password: pwd },
		{ where: { staff_id: staffId } }
	);
	return updatePwd;
};

exports.resetPassword = async (fields) => {
	const salt = bcrypt.genSaltSync(10);
	let pwd = bcrypt.hashSync(fields.new_password, salt);
	let updatePwd = await company.update(
		{ password: pwd, verification_code: null },
		{ where: { verification_code: fields.code } }
	);
	return updatePwd;
};

exports.verifyToken = async (code) => {
	let isVerify = await company.count({
		where: {
			verification_code: code,
		},
	});
	return isVerify;
};

exports.addInterigationKey = async (request) => {
	let obj = {
		company_id: request.company_id,
		integration_key: request.companyIdTokenMoverInventory,
		company_identity: request.company_identity
	}
	return company_integration_key.create(obj);
}

exports.PreparedListModel = async (request) => {
	return await shipment_job.findAll({
		where: {
			shipment_job_id: request.shipmentId
		},
		attributes: [
			"shipment_job_id",
			"storage_shipment_job_id",
			"job_number"
		],
		include: [
			{
				model: shipment_job_assign_worker_list,
				where: {
					shipment_job_id: request.shipmentId,
				},
				attributes: [
					"staff_id",
					"role",
					[
						sequelize.literal("(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
						"first_name",
					],
					[
						sequelize.literal("(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
						"last_name",
					],
				],
				as: "assign_worker",
				include: [
					{
						model: staff,
						attributes: ["email", "company_id"],
						where: {
							is_deleted: 0, status: "active"
						},
						required: false,
						as: "assign_worker_detail",
					},
				],
			},
		],
	});
}

exports.addInterigationKey2 = async (request, company) => {
	let obj = {
		company_id: request.company_id,
		integration_key: `mover#${company.company_identity}#inventory`,
		company_identity: company.company_identity,
		status: "inactive",
	}
	return company_integration_key.create(obj);
}

exports.companyStorageIdUpdate = async (request) => {
	try {
		return company.update(
			{
				storage_company_id: request.companyIdStorage
			},
			{ where: { company_id: request.company_id } }
		);
	}
	catch (err) {
		console.log("err", err);
	}
}

exports.addCompany = async (request, file) => {
	let companyObj = {
		company_name: request.company_name && request.company_name,
		photo: file !== "" ? file : null,
		address1: request.address1 && request.address1,
		address2: request.address2 && request.address2,
		city: request.city && request.city,
		state: request.state && request.state,
		zipCode: request.zipCode && request.zipCode,
		country: request.country && request.country,
		phone: request.phone && request.phone,
		country_code: request.country_code && request.country_code,
		company_identity: request.company_identity && request.company_identity,
		email: request.email && request.email,
		group_id: request.group_id !== "" ? request.group_id : null,
		password: request.password !== "" ? request.password : "123456",
		notes: request.notes && request.notes,
	};


	let companyDetails = await company.create(companyObj);

	let userLoginObj = {
		company_id: companyDetails.company_id,
		first_name: companyDetails.company_name,
		last_name: "Company User",
		email: companyDetails.email,
		password: request.password !== "" ? request.password : "123456",
		roles: "ADMIN"
	}


	let createUserForCompnay = await staff.create(userLoginObj)

	if (companyDetails && createUserForCompnay) {


		let fetchShipmentTypeForCompnay = await shipmentTypeModel.fetchShipmentTypeForCompnay(request);
		let createShipmentTypeForCompnay = await shipmentTypeModel.createShipmentTypeForCompnay(fetchShipmentTypeForCompnay, companyDetails);


		let newArrayData = [];
		for (let i = 0; i < createShipmentTypeForCompnay.length; i++) {
			let container = {};
			container.shipmentId = createShipmentTypeForCompnay[i].shipment_type_id;
			newArrayData.push(container)
		}

		for (let i = 0; i < fetchShipmentTypeForCompnay.length; i++) {
			let fetchShipmentTypeStagesForCompnay = await shipmentTypeModel.fetchShipmentTypeStagesForCompnay(fetchShipmentTypeForCompnay[i].shipment_type_id,);
			let createShipmentTypeStagesForCompnay = await shipmentTypeModel.createShipmentTypeStagesForCompnay(fetchShipmentTypeStagesForCompnay, newArrayData[i].shipmentId);
		}

		let fetchRoomListForCompnay = await roomModel.fetchRoomListForCompnay(request);
		let createRoomListForCompnay = await roomModel.createRoomListForCompnay(fetchRoomListForCompnay, companyDetails);

		let fetchItemListForCompnay = await itemSuggestionModel.fetchItemListForCompnay(request);
		let createItemListForCompnay = await itemSuggestionModel.createItemListForCompnay(fetchItemListForCompnay, companyDetails);




		let fetchTagListForCompnay = await tagModel.fetchTagListForCompnay(request);

		let createTagListForCompnay = await tagModel.createTagListForCompnay(fetchTagListForCompnay, companyDetails);

	}
	return companyDetails;
};


exports.fetchExistingCompanyCustomerData = async (request) => {
	return await customer.findAndCountAll({
		where: {
			company_id: request.companyId,
			is_deleted: "0",
		},
	})
}

exports.fetchExistingStorageCompanyCustomerData = async (request) => {
	return await customer.findAndCountAll({
		where: {
			company_id: request.company_id,
			is_deleted: "0",
		},
	})
}

const checkUnitalReadyExists = async (storageUnitId) => {
	const result = await unit_list.findOne({
		where: { storage_unit_id: storageUnitId },
		raw: true,
	});
	if (result !== null) {
		return false
	}
	else {
		return true
	}
}

exports.fetchNewCompanyUnits = async (unitList) => {
	let newArrayData = [];
	for (let i = 0; i < unitList.length; i++) {
		const checkUnitalReadyExistsResult = await checkUnitalReadyExists(unitList[i].storage_unit_id);
		if (checkUnitalReadyExistsResult) {
			let container = {};
			container.storage_unit_id = unitList[i].storage_unit_id;
			container.name = unitList[i].name;
			container.unitCode = unitList[i].unitCode;
			container.number = unitList[i].number;
			container.unitNotes = unitList[i].unitNotes;
			container.currentLocation = unitList[i].currentLocation;
			container.numericLocation = unitList[i].numericLocation;
			container.addedBy = unitList[i].addedBy;
			container.status = unitList[i].status;
			container.isActive = unitList[i].isActive;
			container.warehouseId = unitList[i].warehouseId;
			container.customerId = unitList[i].customerId;
			container.shipmentId = unitList[i].shipmentId;
			container.roomId = unitList[i].roomId;
			container.unitTypeId = unitList[i].unitTypeId;
			container.unitTypeName = unitList[i].unitTypeName;
			newArrayData.push(container)
		}
	}
	return unit_list.bulkCreate(newArrayData);
}

exports.createUnitModels = async (unitList) => {
	let newArrayData = [];
	for (let i = 0; i < unitList.length; i++) {
		let container = {};
		container.storage_unit_id = unitList[i].id;
		container.name = unitList[i].name;
		container.unitCode = unitList[i].unitCode;
		container.number = unitList[i].number;
		container.unitNotes = unitList[i].unitNotes;
		container.currentLocation = unitList[i].currentLocation;
		container.numericLocation = unitList[i].numericLocation;
		container.addedBy = unitList[i].addedBy;
		container.status = unitList[i].status;
		container.isActive = unitList[i].isActive;
		container.warehouseId = unitList[i].warehouseId;
		container.customerId = unitList[i].customerId;
		container.shipmentId = unitList[i].shipmentId;
		container.roomId = unitList[i].roomId;
		container.unitTypeId = unitList[i].unitTypeId;
		container.unitTypeName = unitList[i].unitTypeName;

		newArrayData.push(container)
	}
	return unit_list.bulkCreate(newArrayData);
}


exports.companyIDBasicController = async (filters) => {
	const dbValue = await company
		.findAll({
			where: {
				is_deleted: 0,
				roles: { [Op.not]: "COMPANYSUPERADMIN" },
				[Op.or]: [
					{ company_identity: { [Op.like]: "%" + filters.search + "%" } },
				],
			},
			attributes: [
				"company_identity",
				"company_id",
				"storage_company_id",
				"email",
			],
			order: [["company_identity"]],
		});
	if (dbValue) {
		return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)))
	}
}

exports.fetchExistingCompanyStaffData = async (request) => {
	return await staff.findAndCountAll({
		where: {
			company_id: request.companyId,
			is_deleted: "0",
			email: {
				[Op.not]: request.email,
			}
		},
	})
}


exports.checkCompanyAccountIdMandatory = async (userDetails) => {
	if (userDetails.staff_id !== null) {
		let getCompanyDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);
		return await company.findOne({
			where: {
				company_id: getCompanyDetails.company_id,
				is_deleted: "0",
				roles: { [Op.not]: "COMPANYSUPERADMIN" }
			},
			attributes:
				[
					"company_id",
					[
						sequelize.literal(
							"(select name FROM `groups` where group_id = company.group_id) "
						),
						"group_name",
					],
					[
						sequelize.literal(
							"(select make_account_id_mandatory FROM `groups` where group_id = company.group_id) "
						),
						"make_account_id_mandatory",
					],
				]
		})
	}
	else {
		return await company.findOne({
			where: {
				company_id: userDetails.company_id,
				is_deleted: "0",
				roles: { [Op.not]: "COMPANYSUPERADMIN" }
			},
			attributes:
				[
					"company_id",
					[
						sequelize.literal(
							"(select name FROM `groups` where group_id = company.group_id) "
						),
						"group_name",
					],
					[
						sequelize.literal(
							"(select make_account_id_mandatory FROM `groups` where group_id = company.group_id) "
						),
						"make_account_id_mandatory",
					],
				]
		})
	}
}


exports.getCompanyList = async (company_id, request) => {
	const pageNo = request.pageNo ? request.pageNo : 1;
	const pageSize = request.pageSize ? request.pageSize : 10000;
	const orderBy = request.orderBy ? request.orderBy : "company_name";
	const orderSequence = request.orderSequence ? request.orderSequence : "ASC";
	const search = request.search ? request.search : "";
	pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);
	let matchObj = {};
	if (company_id) {
		matchObj = {
			[Op.and]: [
				{ company_id: company_id, is_deleted: 0 },
				{
					[Op.or]: [
						{ company_name: { [Op.like]: "%" + search + "%" } },
						{ company_identity: { [Op.like]: "%" + search + "%" } },
						{ email: { [Op.like]: "%" + search + "%" } },
					],
				},
			],
		};
	} else {
		matchObj = {
			[Op.or]: [
				{ company_name: { [Op.like]: "%" + search + "%" } },
				{ company_identity: { [Op.like]: "%" + search + "%" } },
				{ email: { [Op.like]: "%" + search + "%" } },
			],
			is_deleted: "0",
			roles: { [Op.not]: "COMPANYSUPERADMIN" }

		};
	}
	return await company.findAndCountAll({
		limit: pageSize,
		offset: start,
		where: matchObj,
		order: [[orderBy, orderSequence]],
		attributes: [
			...COMMON_COMPANY_ATTRIBUTES,
			[
				sequelize.literal(`(CASE WHEN photo IS NULL THEN '' ELSE photo END)`),
				"photo",
			],
			[
				sequelize.literal(
					"(select company_key FROM `company_tokens` where company_id = company.company_id) "
				),
				"api_key",
			],
			[
				sequelize.literal(
					"(select name FROM `groups` where group_id = company.group_id) "
				),
				"group_name",
			],
			[
				sequelize.literal(
					"(select make_account_id_mandatory FROM `groups` where group_id = company.group_id) "
				),
				"make_account_id_mandatory",
			],
			[
				sequelize.literal(
					"(select isEnable FROM `company_tokens` where company_id = company.company_id) "
				),
				"api_status",
			],
			[
				sequelize.literal(
					"(select integration_key FROM `company_integration_keys` where company_id = company.company_id) "
				),
				"integration_key",
			],
			[
				sequelize.literal(
					"(select status FROM `company_integration_keys` where company_id = company.company_id) "
				),
				"integration_key_status",
			],
			[
				sequelize.literal(
					`(CASE WHEN photo IS NULL THEN '' WHEN photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Company_Profile}', 'original/', photo) ELSE photo END)`
				),
				"company_logo",
			],
		],
		raw: true,
	});
};

exports.changeCompanyStatus = async (request) => {
	return await company.update(
		{
			status: literal(
				'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
			),
		},
		{ where: { company_id: request.company_id } }
	);
};

exports.changeStaffStatusStorage = async (request) => {
	if (request.isDeleted) {
		return await staff.update(
			{
				is_deleted: 1,
			},
			{ where: { storage_staff_id: request.staff_id } }
		);
	}
	else {
		return await staff.update(
			{
				status: request.isActive == 1 ? "active" : "inactive"
			},
			{ where: { storage_staff_id: request.staff_id } }
		);
	}


};

exports.changeCompanyStatusStorage = async (request) => {
	if (request.isDeleted) {
		return await company.update(
			{
				is_deleted: 1,
			},
			{ where: { storage_company_id: request.company_id } }
		);
	}
	else {
		return await company.update(
			{
				status: literal(
					'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
				),
			},
			{ where: { storage_company_id: request.company_id } }
		);
	}
};

exports.changeIntegrationKeyStatusController = async (request) => {

	return await company_integration_key.update(
		{
			status: literal(
				'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
			),
		},
		{ where: { company_id: request.company_id } }
	);
};

exports.findCompanyIdTokenMoverInventory = async (request) => {
	return await company_integration_key.findOne(
		{ where: { integration_key: request.companyIdTokenMoverInventory } }
	);
};

exports.updateIntegrationKeyStatusController = async (request) => {
	return await company_integration_key.update(
		{
			status: literal(
				'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
			),
		},
		{ where: { integration_key: request.companyIdTokenMoverInventory } }
	);
};

exports.companyStorageIdTokenMoverInventoryUpdate = async (request, companyData) => {
	return await company.update(
		{
			storage_company_id: request.id
		},
		{ where: { company_id: companyData.company_id } }
	);
};

exports.viewCompany = async (req_company_id, company_id) => {
	let matchObj = {};
	if (req_company_id) {
		matchObj = {
			[Op.and]: [{ company_id: req_company_id }, { company_id: company_id }],
		};
	} else {
		matchObj = { company_id: company_id };
	}
	return await company.findOne({
		attributes: [
			...COMMON_COMPANY_ATTRIBUTES,
			[
				sequelize.literal(`(CASE WHEN photo IS NULL THEN '' ELSE photo END)`),
				"photo",
			],
			[
				sequelize.literal(
					`(CASE WHEN photo IS NULL THEN '' WHEN photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Company_Profile}', 'original/', photo) ELSE photo END)`
				),
				"company_logo",
			],
		],
		where: matchObj
	});
};

exports.fetchCompanyIntegrationToken = async (body) => {
	return await company.findOne({
		attributes: [
			...COMMON_COMPANY_ATTRIBUTES,
			[
				sequelize.literal(`(CASE WHEN photo IS NULL THEN '' ELSE photo END)`),
				"photo",
			],
			[
				sequelize.literal(
					`(CASE WHEN photo IS NULL THEN '' WHEN photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Company_Profile}', 'original/', photo) ELSE photo END)`
				),
				"company_logo",
			],
		],
		where: {
			company_id: body.company_id
		}
	});
};

exports.checkExistingIdentity = async (identity) => {
	return await company.count({
		where: {
			company_identity: identity,
			is_deleted: "0"
		},
	});
};

exports.checkExistingEmail = async (email) => {
	return await company.count({
		where: {
			email: email,
			is_deleted: "0"
		},
	});
};

exports.checkReExistingEmail = async (request) => {
	return await company.count({
		where: {
			email: request.email,
			company_id: { [Op.ne]: request.company_id },
		},
	});
};

exports.checkReExistingIdentity = async (request) => {
	return await company.count({
		where: {
			company_identity: request.company_identity,
			company_id: { [Op.ne]: request.company_id },
		},
	});
};

exports.editCompany = async (request, file) => {
	let companyObj = {
		company_name: request.company_name && request.company_name,
		address1: request.address1 && request.address1,
		address2: request.address2 && request.address2,
		city: request.city && request.city,
		state: request.state && request.state,
		zipCode: request.zipCode && request.zipCode,
		country: request.country && request.country,
		phone: request.phone && request.phone,
		country_code: request.country_code && request.country_code,
		company_identity: request.company_identity && request.company_identity,
		email: request.email && request.email,
		notes: request.notes && request.notes,
		group_id: request.group_id !== "" ? request.group_id : null,
		updated_at: new Date(),
	};

	companyObj["photo"] = file !== "" && file !== null && file !== undefined ? file : null;

	return await company.update(
		companyObj,
		{ where: { company_id: request.company_id } }
	);
};

exports.deleteCompany = async (request) => {
	return await company.update(
		{
			is_deleted: literal('CASE WHEN is_deleted = "0" THEN "1" ELSE "0" END'),
		},
		{ where: { company_id: request.company_id } }
	);
};

exports.isValidCompanyModel = async (companyId) => {
	//newChanges
	const isValid = await company
		.findOne({
			where: { company_id: companyId },
			attributes: ["company_id"],
			raw: true,
		});
	if (isValid) {
		return isValid !== null
	}
	else {
		return false
	}
}

exports.getCustomerStorageId = async (customerId) => {
	return customer.findOne({
		where: {
			customer_id: customerId
		},
		attributes: [
			"customer_id",
			"storage_customer_id"
		]
	})
}

exports.getIntegrationKeyToken = async (companyId) => {
	return company_integration_key.findOne({
		where: {
			company_id: companyId
		},
		attributes: [
			"company_integration_key_id",
			"company_id",
			"integration_key",
			"company_identity",
			"status",
		],
		include: {
			model: company,
			as: "key_company",
			attributes: ["company_name", "company_id", "storage_company_id"],
		},
	})
}


exports.getIntegrationKeyData = async (request) => {
	return company_integration_key.findOne({
		where: {
			company_id: request.company_id
		},
		attributes: [
			"company_integration_key_id",
			"company_id",
			"integration_key",
			"company_identity",
			"status",
		],
		include: {
			model: company,
			as: "key_company",
			attributes: ["company_name", "company_id", "storage_company_id"],
		},
	})
}

exports.getIntegrationKeyDataCustomerFetch = async (companyId) => {
	return await company_integration_key.findOne({
		where: {
			company_id: companyId
		},
		attributes: [
			"company_integration_key_id",
			"company_id",
			"integration_key",
			"company_identity",
			"status",
		],
		include: {
			model: company,
			as: "key_company",
			attributes: ["company_name", "company_id", "storage_company_id"],
		},
	})
}

exports.getIntegrationKeyDataCustomer = async (request) => {
	if (request.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(request.staff_id);
		return company_integration_key.findOne({
			where: {
				company_id: getStaffDetails.company_id
			},
			attributes: [
				"company_integration_key_id",
				"company_id",
				"integration_key",
				"company_identity",
				"status",
			],
			include: {
				model: company,
				as: "key_company",
				attributes: ["company_name", "company_id", "storage_company_id"],
			},
		})

	}
	else {
		return company_integration_key.findOne({
			where: {
				company_id: request.company_id
			},
			attributes: [
				"company_integration_key_id",
				"company_id",
				"integration_key",
				"company_identity",
				"status",
			],
			include: {
				model: company,
				as: "key_company",
				attributes: ["company_name", "company_id", "storage_company_id"],
			},
		})
	}
}

exports.getIntegrationKeyController = async (getUserDetails) => {
	if (getUserDetails.company_id !== null) {
		return company_integration_key.findOne({
			where: {
				company_id: getUserDetails.company_id
			},
			attributes: [
				"company_integration_key_id",
				"company_id",
				"integration_key",
				"company_identity",
				"status",
			],
			include: {
				model: company,
				as: "key_company",
				attributes: ["company_name", "company_id", "storage_company_id"],
			},
		})
	}
	else if (getUserDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		return company_integration_key.findOne({
			where: {
				company_id: getStaffDetails.company_id
			},
			attributes: [
				"company_integration_key_id",
				"company_id",
				"integration_key",
				"company_identity",
				"status"
			],
			include: {
				model: company,
				as: "key_company",
				attributes: ["company_name", "company_id", "storage_company_id"],
			},
		})
	}
	return null
}
