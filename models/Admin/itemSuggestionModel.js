const { item_suggestion: item, item_iso_code } = require("../../database/schemas");
const { Op, literal } = require("sequelize");
const StaffModel = require("../../models/Admin/staffModel");


exports.fetchItemListForCompnay = async (request) => {
	return await item.findAll({
		where: {
			company_id: "-1",
			status: "active",
		},
	});
}

exports.createItemListForCompnay = async (fetchItemListForCompnay, companyDetails) => {
	let newArrayData = [];
	for (let i = 0; i < fetchItemListForCompnay.length; i++) {
		let container = {};
		container.name = fetchItemListForCompnay[i].name;
		container.volume = fetchItemListForCompnay[i].volume;
		container.weight = fetchItemListForCompnay[i].weight;
		container.admin_id = null;
		container.company_id = companyDetails.company_id;
		container.staff_id = null;
		container.status = fetchItemListForCompnay.status;

		// Check for ISO code and set it, otherwise use 999
		let itemIsoCode = await exports.itemIsoCode(fetchItemListForCompnay[i].name);
		container.iso_code = (itemIsoCode && itemIsoCode.code) ? itemIsoCode.code : "999";

		newArrayData.push(container)
	}
	return await item.bulkCreate(newArrayData);
}



exports.getItemListingModel = async (fieldsAndValues, userDetails) => {

	if (userDetails.admin_id !== null) {
		return await item.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 50,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				admin_id: {
					[Op.not]: null,
				},
				[Op.or]: [
					{ name: { [Op.like]: '%' + fieldsAndValues.search + '%' } },
					{ iso_code: { [Op.like]: '%' + fieldsAndValues.search + '%' } }
				],
				status: fieldsAndValues.filter ? fieldsAndValues.filter : "active",
			},
			attributes: ["item_suggestion_id", "iso_code", "name", "volume", "weight", "status", "admin_id", "company_id", "staff_id"],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "DESC",
				],
			],
		});
	}
	else if (userDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);
		return await item.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 50,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				company_id: getStaffDetails.company_id,
				status: fieldsAndValues.filter ? fieldsAndValues.filter : "active",
				[Op.or]: [
					{ name: { [Op.like]: fieldsAndValues.search + "%" } },
					{ iso_code: { [Op.like]: fieldsAndValues.search + "%" } }
				]
			},
			attributes: ["item_suggestion_id", "iso_code", "name", "volume", "weight", "status", "admin_id", "company_id", "staff_id"],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "DESC",
				],
			],
		});
	}

	else {

		return await item.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				company_id: userDetails.company_id,
				[Op.or]: [
					{ name: { [Op.like]: '%' + fieldsAndValues.search + '%' } },
					{ iso_code: { [Op.like]: '%' + fieldsAndValues.search + '%' } }
				],
				status: fieldsAndValues.filter ? fieldsAndValues.filter : "active",
			},
			attributes: ["item_suggestion_id", "iso_code", "name", "volume", "weight", "status", "admin_id", "company_id", "staff_id"],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "DESC",
				],
			],
		});
	}

}

exports.createItemSuggestionModel = async (itemData, getUserDetails) => {
	let itemDetails;

	if (getUserDetails.admin_id !== null) {
		itemDetails = await item.create({
			name: itemData.item_name,
			volume: itemData.item_volume,
			weight: itemData.item_weight,
			admin_id: getUserDetails.admin_id,
			company_id: (itemData?.add_permanently == false || itemData?.add_permanently == "false") ? null : "-1",
			staff_id: getUserDetails.staff_id
		});
	}
	else if (getUserDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		itemDetails = await item.create({
			name: itemData.item_name,
			volume: itemData.item_volume,
			weight: itemData.item_weight,
			admin_id: getUserDetails.admin_id,
			company_id: (itemData?.add_permanently == false || itemData?.add_permanently == "false") ? null : getStaffDetails.company_id,
			staff_id: getUserDetails.staff_id
		});
	}
	else {
		itemDetails = await item.create({
			name: itemData.item_name,
			volume: itemData.item_volume,
			weight: itemData.item_weight,
			admin_id: getUserDetails.admin_id,
			company_id: (itemData?.add_permanently == false || itemData?.add_permanently == "false") ? null : getUserDetails.company_id,
			staff_id: getUserDetails.staff_id
		});
	}

	// Update the newly created item with ISO code if found, otherwise use 999
	if (itemDetails) {
		let itemIsoCode = await exports.itemIsoCode(itemData.item_name);
		const isoCodeToUpdate = (itemIsoCode && itemIsoCode.code) ? itemIsoCode.code : "999";
		await exports.updateItemIsoCode(itemDetails.item_suggestion_id, isoCodeToUpdate);
	}

	return itemDetails;
}


exports.editItemModel = async (itemId, itemData, getUserDetails) => {
	let editItem;

	if (getUserDetails.admin_id !== null) {
		editItem = await item.update(
			{
				name: itemData.item_name,
				volume: itemData.item_volume,
				weight: itemData.item_weight,
				company_id: "-1",
				admin_id: getUserDetails.admin_id,
				staff_id: getUserDetails.staff_id
			},
			{ where: { item_suggestion_id: itemId } }
		);
	}
	else if (getUserDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		editItem = await item.update(
			{
				name: itemData.item_name,
				volume: itemData.item_volume,
				weight: itemData.item_weight,
				company_id: getStaffDetails.company_id,
				admin_id: getUserDetails.admin_id,
				staff_id: getUserDetails.staff_id
			},
			{ where: { item_suggestion_id: itemId } }
		);
	}
	else {
		editItem = await item.update(
			{
				name: itemData.item_name,
				volume: itemData.item_volume,
				weight: itemData.item_weight,
				company_id: getUserDetails.company_id,
				admin_id: getUserDetails.admin_id,
				staff_id: getUserDetails.staff_id
			},
			{ where: { item_suggestion_id: itemId } }
		);
	}

	// Update the item with ISO code if found, otherwise use 999
	if (editItem && editItem[0] > 0) { // editItem[0] contains the number of affected rows
		let itemIsoCode = await exports.itemIsoCode(itemData.item_name);
		const isoCodeToUpdate = (itemIsoCode && itemIsoCode.code) ? itemIsoCode.code : "999";
		await exports.updateItemIsoCode(itemId, isoCodeToUpdate);
	}

	return editItem;
}

exports.findOldStatusitemSuggestion = async (itemId) => {
	return item.findOne({
		where: {
			item_suggestion_id: itemId
		}
	})
}

exports.changeItemStatus = async (itemId) =>
	await item.update(
		{
			status: literal(
				'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
			),
		},
		{ where: { item_suggestion_id: itemId } }
	);

exports.removeItemModel = async (itemId) =>
	await item.destroy({ where: { item_suggestion_id: itemId } });

exports.getItemModel = async (itemId) => await item.findByPk(itemId);

exports.getItemViaNameModel = async (itemName) =>
	await item.findOne({ where: { name: itemName } });

exports.checkItemExistenceModel = async (itemId) => {
	//newChanges
	const isItem = await item
		.findOne({
			where: { item_suggestion_id: itemId },
			attributes: ["item_suggestion_id"],
			raw: true,
		});
	if (isItem !== null) {
		return true
	}
	else {
		return false
	}
}

exports.batchItemListStatusChange = async (itemList, isActiveFlag) =>
	await item.update(
		{
			status: isActiveFlag ? "Active" : "Inactive"
		},
		{ where: { item_suggestion_id: { [Op.in]: itemList } } }
	);

exports.batchDeleteItemList = async (itemList) =>
	await item.destroy(
		{ where: { item_suggestion_id: { [Op.in]: itemList } } }
	);

exports.itemIsoCode = async (itemName) => {
	return item_iso_code.findOne({
		where: {
			name: itemName,
		}
	});
};

exports.updateItemIsoCode = async (itemId, isoCode) => {
	return await item.update(
		{ iso_code: isoCode },
		{ where: { item_suggestion_id: itemId } }
	);
};
